{"name": "sari-sari-admin", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@headlessui/react": "^2.2.4", "@hookform/resolvers": "^5.1.1", "@supabase/supabase-js": "^2.50.3", "cloudinary": "^2.7.0", "date-fns": "^4.1.0", "echarts": "^5.6.0", "echarts-for-react": "^3.0.2", "framer-motion": "^12.23.0", "lucide-react": "^0.525.0", "next": "15.3.5", "next-cloudinary": "^6.16.0", "next-themes": "^0.4.6", "react": "^19.0.0", "react-dom": "^19.0.0", "react-hook-form": "^7.60.0", "react-icons": "^5.5.0", "zod": "^3.25.75"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.3.5", "tailwindcss": "^4", "typescript": "^5"}}