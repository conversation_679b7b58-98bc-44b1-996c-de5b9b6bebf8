export { Axis, ChartView, ZRColor as Color, ComponentModel, ComponentView, ComposeOption, DownplayPayload, ECElementEvent, EChartsType as ECharts, ECBasicOption as EChartsCoreOption, EChartsInitOpts, EChartsType, ElementEvent, HighlightPayload, ImagePatternObject, LinearGradientObject, SeriesData as List, Model, PRIORITY, PatternObject, Payload, RadialGradientObject, ResizeOpts, SVGPatternObject, SelectChangedPayload, SeriesModel, SetOptionOpts, SetOptionTransitionOpt, SetOptionTransitionOptItem, color_d as color, connect, dataTool, dependencies, disConnect, disconnect, dispose, env, extendChartView, extendComponentModel, extendComponentView, extendSeriesModel, format_d as format, getCoordinateSystemDimensions, getInstanceByDom, getInstanceById, getMap, graphic_d as graphic, helper_d as helper, init, brushSingle as innerDrawElementOnCanvas, matrix_d as matrix, number_d as number, parseGeoJSON, parseGeoJSON as parseGeoJson, registerAction, registerCoordinateSystem, registerLayout, registerLoading, registerLocale, registerMap, registerPostInit, registerPostUpdate, registerPreprocessor, registerProcessor, registerTheme, registerTransform, registerUpdateLifecycle, registerVisual, setCanvasCreator, setPlatformAPI, throttle, time_d as time, use, util_d$1 as util, vector_d as vector, version, util_d as zrUtil, zrender_d as zrender } from './shared';