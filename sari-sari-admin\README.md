# Revantad Store - Professional Admin Dashboard

A comprehensive web-based admin dashboard for managing products and customer debt (utang) in your Revantad Store. Built with Next.js, TypeScript, Tailwind CSS, Supabase, and Cloudinary with modern UI/UX design.

## Features

### 🏪 Professional Landing Page
- ✅ Beautiful hero section with Green and Mustard theme
- ✅ Feature showcase and customer testimonials
- ✅ Responsive design with modern animations
- ✅ Professional branding for Revantad Store

### 📱 Facebook-Style Admin Header
- ✅ Logo with return to front page functionality
- ✅ Advanced search bar for products, debts, and more
- ✅ Navigation icons for Dashboard, Products, Debts, Family Gallery
- ✅ Dark mode toggle and admin profile dropdown

### 📊 Product Lists (CRUD Operations)
- ✅ Create, Read, Update, Delete product entries
- ✅ Product image upload to Cloudinary
- ✅ Product details: name, net weight, price, stock quantity, category
- ✅ Low stock alerts and category filtering
- ✅ Advanced search functionality
- ✅ Responsive product grid layout

### 👥 Customer Debt Management (CRUD Operations)
- ✅ Create, Read, Update, Delete customer debt records
- ✅ Customer details: first name, family name
- ✅ Product details: name, price at time of debt, quantity
- ✅ Automatic total amount calculation
- ✅ Debt date tracking and customer grouping
- ✅ Search functionality by customer or product name

### 📈 API Graphing & Visuals (ECharts)
- ✅ Monthly sales revenue line charts
- ✅ Weekly customer debt trends bar charts
- ✅ Product categories distribution pie charts
- ✅ Real-time KPI cards with business metrics
- ✅ Interactive data visualization

### 🖼️ Family Gallery
- ✅ Upload and manage family photos
- ✅ Like system and photo details
- ✅ Photo statistics and organization
- ✅ Full-screen photo viewing

### 📅 Calendar System
- ✅ Monthly calendar view with events
- ✅ Event management (delivery, meetings, reminders)
- ✅ Event categorization and scheduling
- ✅ Upcoming events overview

### 📋 History Tracking
- ✅ Complete activity logs for all actions
- ✅ Advanced filtering by type and date
- ✅ Export functionality for reports
- ✅ Activity statistics and insights

### ⚙️ Settings Management
- ✅ Store configuration and business hours
- ✅ Notification preferences
- ✅ Backup and data management
- ✅ Security and appearance settings

### 🌙 Dark Mode Support
- ✅ Complete dark theme implementation
- ✅ System theme detection
- ✅ Smooth theme transitions
- ✅ Persistent theme preferences

### 🎨 Modern UI/UX
- ✅ Green and Mustard color scheme
- ✅ Professional typography (Inter & Poppins)
- ✅ Smooth animations with Framer Motion
- ✅ Responsive design for all devices
- ✅ Loading states and error handling

## Tech Stack

- **Frontend**: Next.js 15, React 18, TypeScript
- **Styling**: Tailwind CSS v4 with custom Green/Mustard theme
- **Database**: Supabase (PostgreSQL)
- **Image Storage**: Cloudinary
- **Charts**: ECharts with echarts-for-react
- **Animations**: Framer Motion
- **Icons**: Lucide React, React Icons
- **Theme**: next-themes for dark mode
- **Date Handling**: date-fns
- **Form Handling**: React Hook Form with Zod validation

## Prerequisites

Before you begin, ensure you have:
- Node.js 18+ installed
- A Supabase account and project
- A Cloudinary account
- Git installed

## Setup Instructions

### 1. Clone the Repository

```bash
git clone <your-repo-url>
cd sari-sari-admin
```

### 2. Install Dependencies

```bash
npm install
```

### 3. Set up Supabase

1. Create a new project at [supabase.com](https://supabase.com)
2. Go to Settings > API to get your project URL and anon key
3. Go to SQL Editor and run the schema from `database/schema.sql`

### 4. Set up Cloudinary

1. Create an account at [cloudinary.com](https://cloudinary.com)
2. Go to Dashboard to get your cloud name, API key, and API secret
3. Create an upload preset named `sari-sari-products` (unsigned)

### 5. Environment Variables

Create a `.env.local` file in the root directory and add:

```env
# Supabase Configuration
NEXT_PUBLIC_SUPABASE_URL=your_supabase_project_url
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key
SUPABASE_SERVICE_ROLE_KEY=your_supabase_service_role_key

# Cloudinary Configuration
NEXT_PUBLIC_CLOUDINARY_CLOUD_NAME=your_cloudinary_cloud_name
CLOUDINARY_API_KEY=your_cloudinary_api_key
CLOUDINARY_API_SECRET=your_cloudinary_api_secret

# App Configuration
NEXTAUTH_SECRET=your_nextauth_secret
NEXTAUTH_URL=http://localhost:3000
```

### 6. Database Setup

The database schema includes:

**Products Table:**
- id (UUID, Primary Key)
- name (VARCHAR, Required)
- image_url (TEXT, Optional)
- net_weight (VARCHAR, Required)
- price (DECIMAL, Required)
- stock_quantity (INTEGER, Default: 0)
- category (VARCHAR, Required)
- created_at, updated_at (Timestamps)

**Customer Debts Table:**
- id (UUID, Primary Key)
- customer_name (VARCHAR, Required)
- customer_family_name (VARCHAR, Required)
- product_name (VARCHAR, Required)
- product_price (DECIMAL, Required)
- quantity (INTEGER, Required)
- total_amount (DECIMAL, Calculated)
- debt_date (DATE, Required)
- created_at, updated_at (Timestamps)

### 7. Run the Development Server

```bash
npm run dev
```

Open [http://localhost:3000](http://localhost:3000) to view the dashboard.

## Usage Guide

### Dashboard Overview
- View key statistics: total products, customer debts, debt amounts, and low stock items
- Quick access to main features

### Managing Product Lists
1. Click "Product Lists" in the sidebar
2. Use "Add to Product List" to create new product entries
3. Upload product images via the image upload field
4. Edit or delete products using the action buttons
5. Use search and category filters to find specific products

### Managing Customer Debts
1. Click "Customer Debts" in the sidebar
2. Use "Add Debt Record" to create new debt entries
3. Records are grouped by customer showing total amounts owed
4. Edit or delete individual debt records
5. Use search to find specific customers or products

## API Endpoints

### Products
- `GET /api/products` - Get all products
- `POST /api/products` - Create new product
- `GET /api/products/[id]` - Get single product
- `PUT /api/products/[id]` - Update product
- `DELETE /api/products/[id]` - Delete product

### Customer Debts
- `GET /api/debts` - Get all debt records
- `POST /api/debts` - Create new debt record
- `GET /api/debts/[id]` - Get single debt record
- `PUT /api/debts/[id]` - Update debt record
- `DELETE /api/debts/[id]` - Delete debt record

### File Upload
- `POST /api/upload` - Upload image to Cloudinary

## Project Structure

```
sari-sari-admin/
├── src/
│   ├── app/
│   │   ├── api/          # API routes
│   │   ├── globals.css   # Global styles
│   │   ├── layout.tsx    # Root layout
│   │   └── page.tsx      # Main dashboard page
│   └── components/       # React components
│       ├── Sidebar.tsx
│       ├── DashboardStats.tsx
│       ├── ProductsSection.tsx
│       ├── ProductModal.tsx
│       ├── DebtsSection.tsx
│       └── DebtModal.tsx
├── lib/
│   ├── supabase.ts      # Supabase configuration
│   └── cloudinary.ts    # Cloudinary configuration
├── database/
│   └── schema.sql       # Database schema
└── public/              # Static assets
```

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

## License

This project is licensed under the MIT License.

## Support

For support or questions, please create an issue in the repository.
