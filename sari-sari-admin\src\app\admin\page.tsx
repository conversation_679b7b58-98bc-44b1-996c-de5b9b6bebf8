'use client'

import { useState, useEffect } from 'react'
import <PERSON><PERSON>Header from '@/components/AdminHeader'
import Sidebar from '@/components/Sidebar'
import ProductsSection from '@/components/ProductsSection'
import DebtsSection from '@/components/DebtsSection'
import DashboardStats from '@/components/DashboardStats'
import FamilyGallery from '@/components/FamilyGallery'
import APIGraphing from '@/components/APIGraphing'
import History from '@/components/History'
import Calendar from '@/components/Calendar'
import Settings from '@/components/Settings'
import ProtectedRoute from '@/components/ProtectedRoute'

export default function AdminPage() {
  const [activeSection, setActiveSection] = useState('dashboard')
  const [stats, setStats] = useState({
    totalProducts: 0,
    totalDebts: 0,
    totalDebtAmount: 0,
    lowStockProducts: 0
  })

  useEffect(() => {
    fetchStats()
  }, [])

  const fetchStats = async () => {
    try {
      // Fetch products
      const productsRes = await fetch('/api/products')
      const productsData = await productsRes.json()
      const products = productsData.products || []

      // Fetch debts
      const debtsRes = await fetch('/api/debts')
      const debtsData = await debtsRes.json()
      const debts = debtsData.debts || []

      // Calculate stats
      const totalDebtAmount = debts.reduce((sum: number, debt: { total_amount: number }) => sum + debt.total_amount, 0)
      const lowStockProducts = products.filter((product: { stock_quantity: number }) => product.stock_quantity < 10).length

      setStats({
        totalProducts: products.length,
        totalDebts: debts.length,
        totalDebtAmount,
        lowStockProducts
      })
    } catch (error) {
      console.error('Error fetching stats:', error)
    }
  }

  const renderContent = () => {
    switch (activeSection) {
      case 'products':
        return <ProductsSection onStatsUpdate={fetchStats} />
      case 'debts':
        return <DebtsSection onStatsUpdate={fetchStats} />
      case 'family-gallery':
        return <FamilyGallery />
      case 'api-graphing':
        return <APIGraphing stats={stats} />
      case 'history':
        return <History />
      case 'calendar':
        return <Calendar />
      case 'settings':
        return <Settings />
      default:
        return <DashboardStats stats={stats} />
    }
  }

  const getPageTitle = () => {
    switch (activeSection) {
      case 'dashboard':
        return 'Dashboard'
      case 'products':
        return 'Product Lists'
      case 'debts':
        return 'Customer Debt Management'
      case 'family-gallery':
        return 'Family Gallery'
      case 'api-graphing':
        return 'API Graphing & Visuals'
      case 'history':
        return 'History'
      case 'calendar':
        return 'Calendar'
      case 'settings':
        return 'Settings'
      default:
        return 'Dashboard'
    }
  }

  const getPageDescription = () => {
    switch (activeSection) {
      case 'dashboard':
        return 'Overview of your Revantad Store'
      case 'products':
        return 'Manage your product lists with CRUD operations'
      case 'debts':
        return 'Track customer debt and payments'
      case 'family-gallery':
        return 'Manage family photos and memories'
      case 'api-graphing':
        return 'Visual analytics and business insights'
      case 'history':
        return 'View transaction and activity history'
      case 'calendar':
        return 'Manage events and schedules'
      case 'settings':
        return 'Configure your store settings'
      default:
        return 'Overview of your Revantad Store'
    }
  }

  return (
    <ProtectedRoute>
      <div className="min-h-screen bg-gray-50 dark:bg-slate-900">
        {/* Facebook-style Header */}
        <AdminHeader
          activeSection={activeSection}
          setActiveSection={setActiveSection}
        />

        <div className="flex pt-16">
          {/* Updated Sidebar */}
          <Sidebar activeSection={activeSection} setActiveSection={setActiveSection} />

          {/* Main Content */}
          <main className="flex-1 overflow-auto">
            <div className="p-8">
              <div className="mb-8">
                <h1 className="text-3xl font-bold text-gray-900 dark:text-white">
                  {getPageTitle()}
                </h1>
                <p className="text-gray-600 dark:text-gray-300 mt-2">
                  {getPageDescription()}
                </p>
              </div>
              {renderContent()}
            </div>
          </main>
        </div>
      </div>
    </ProtectedRoute>
  )
}
