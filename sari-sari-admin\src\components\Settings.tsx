'use client'

import { useState } from 'react'
import { Save, User, Store, Bell, Shield, Palette, Database, Download, Upload } from 'lucide-react'

export default function Settings() {
  const [activeTab, setActiveTab] = useState('store')
  const [settings, setSettings] = useState({
    store: {
      name: 'Revantad Store',
      address: '123 Barangay Street, Manila, Philippines',
      phone: '+63 ************',
      email: '<EMAIL>',
      currency: 'PHP',
      timezone: 'Asia/Manila',
      businessHours: {
        open: '06:00',
        close: '22:00',
      },
    },
    profile: {
      firstName: 'Admin',
      lastName: 'User',
      email: '<EMAIL>',
      phone: '+63 ************',
      role: 'Store Owner',
    },
    notifications: {
      lowStock: true,
      newDebt: true,
      paymentReceived: true,
      dailyReport: false,
      weeklyReport: true,
      emailNotifications: true,
      smsNotifications: false,
    },
    security: {
      twoFactorAuth: false,
      sessionTimeout: '30',
      passwordExpiry: '90',
      loginAttempts: '5',
    },
    appearance: {
      theme: 'light',
      language: 'en',
      dateFormat: 'MM/DD/YYYY',
      numberFormat: 'en-US',
    },
    backup: {
      autoBackup: true,
      backupFrequency: 'daily',
      retentionDays: '30',
      lastBackup: '2024-01-20T10:30:00Z',
    },
  })

  const tabs = [
    { id: 'store', label: 'Store Info', icon: Store },
    { id: 'profile', label: 'Profile', icon: User },
    { id: 'notifications', label: 'Notifications', icon: Bell },
    { id: 'security', label: 'Security', icon: Shield },
    { id: 'appearance', label: 'Appearance', icon: Palette },
    { id: 'backup', label: 'Backup', icon: Database },
  ]

  const handleSave = () => {
    // Save settings logic here
    console.log('Settings saved:', settings)
    alert('Settings saved successfully!')
  }

  const handleExportData = () => {
    // Export data logic here
    console.log('Exporting data...')
    alert('Data export started. You will receive an email when ready.')
  }

  const handleImportData = () => {
    // Import data logic here
    console.log('Importing data...')
    alert('Please select a backup file to import.')
  }

  const renderStoreSettings = () => (
    <div className="space-y-6">
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div>
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            Store Name
          </label>
          <input
            type="text"
            value={settings.store.name}
            onChange={(e) => setSettings({
              ...settings,
              store: { ...settings.store, name: e.target.value }
            })}
            className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-2 focus:ring-green-500 focus:border-transparent dark:bg-slate-700"
          />
        </div>
        
        <div>
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            Phone Number
          </label>
          <input
            type="tel"
            value={settings.store.phone}
            onChange={(e) => setSettings({
              ...settings,
              store: { ...settings.store, phone: e.target.value }
            })}
            className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-2 focus:ring-green-500 focus:border-transparent dark:bg-slate-700"
          />
        </div>
      </div>
      
      <div>
        <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
          Address
        </label>
        <textarea
          value={settings.store.address}
          onChange={(e) => setSettings({
            ...settings,
            store: { ...settings.store, address: e.target.value }
          })}
          rows={3}
          className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-2 focus:ring-green-500 focus:border-transparent dark:bg-slate-700"
        />
      </div>
      
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div>
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            Opening Time
          </label>
          <input
            type="time"
            value={settings.store.businessHours.open}
            onChange={(e) => setSettings({
              ...settings,
              store: { 
                ...settings.store, 
                businessHours: { ...settings.store.businessHours, open: e.target.value }
              }
            })}
            className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-2 focus:ring-green-500 focus:border-transparent dark:bg-slate-700"
          />
        </div>
        
        <div>
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            Closing Time
          </label>
          <input
            type="time"
            value={settings.store.businessHours.close}
            onChange={(e) => setSettings({
              ...settings,
              store: { 
                ...settings.store, 
                businessHours: { ...settings.store.businessHours, close: e.target.value }
              }
            })}
            className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-2 focus:ring-green-500 focus:border-transparent dark:bg-slate-700"
          />
        </div>
      </div>
    </div>
  )

  const renderNotificationSettings = () => (
    <div className="space-y-6">
      <div>
        <h4 className="text-lg font-medium text-gray-900 dark:text-white mb-4">Alert Preferences</h4>
        <div className="space-y-4">
          {Object.entries(settings.notifications).map(([key, value]) => (
            <div key={key} className="flex items-center justify-between">
              <div>
                <label className="text-sm font-medium text-gray-700 dark:text-gray-300">
                  {key.replace(/([A-Z])/g, ' $1').replace(/^./, str => str.toUpperCase())}
                </label>
                <p className="text-xs text-gray-500 dark:text-gray-400">
                  {key === 'lowStock' && 'Get notified when products are running low'}
                  {key === 'newDebt' && 'Alert when new customer debt is recorded'}
                  {key === 'paymentReceived' && 'Notification for debt payments'}
                  {key === 'dailyReport' && 'Daily business summary report'}
                  {key === 'weeklyReport' && 'Weekly business analytics report'}
                  {key === 'emailNotifications' && 'Receive notifications via email'}
                  {key === 'smsNotifications' && 'Receive notifications via SMS'}
                </p>
              </div>
              <label className="relative inline-flex items-center cursor-pointer">
                <input
                  type="checkbox"
                  checked={value as boolean}
                  onChange={(e) => setSettings({
                    ...settings,
                    notifications: { ...settings.notifications, [key]: e.target.checked }
                  })}
                  className="sr-only peer"
                />
                <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-green-300 dark:peer-focus:ring-green-800 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-green-600"></div>
              </label>
            </div>
          ))}
        </div>
      </div>
    </div>
  )

  const renderBackupSettings = () => (
    <div className="space-y-6">
      <div>
        <h4 className="text-lg font-medium text-gray-900 dark:text-white mb-4">Backup Configuration</h4>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Backup Frequency
            </label>
            <select
              value={settings.backup.backupFrequency}
              onChange={(e) => setSettings({
                ...settings,
                backup: { ...settings.backup, backupFrequency: e.target.value }
              })}
              className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-2 focus:ring-green-500 focus:border-transparent dark:bg-slate-700"
            >
              <option value="daily">Daily</option>
              <option value="weekly">Weekly</option>
              <option value="monthly">Monthly</option>
            </select>
          </div>
          
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Retention Period (Days)
            </label>
            <input
              type="number"
              value={settings.backup.retentionDays}
              onChange={(e) => setSettings({
                ...settings,
                backup: { ...settings.backup, retentionDays: e.target.value }
              })}
              className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-2 focus:ring-green-500 focus:border-transparent dark:bg-slate-700"
            />
          </div>
        </div>
      </div>
      
      <div className="border-t border-gray-200 dark:border-gray-700 pt-6">
        <h4 className="text-lg font-medium text-gray-900 dark:text-white mb-4">Data Management</h4>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <button
            onClick={handleExportData}
            className="flex items-center justify-center px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-md hover:bg-gray-50 dark:hover:bg-slate-700"
          >
            <Download className="h-5 w-5 mr-2" />
            Export All Data
          </button>
          
          <button
            onClick={handleImportData}
            className="flex items-center justify-center px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-md hover:bg-gray-50 dark:hover:bg-slate-700"
          >
            <Upload className="h-5 w-5 mr-2" />
            Import Data
          </button>
        </div>
        
        <div className="mt-4 p-4 bg-gray-50 dark:bg-slate-700 rounded-md">
          <p className="text-sm text-gray-600 dark:text-gray-400">
            <strong>Last Backup:</strong> {new Date(settings.backup.lastBackup).toLocaleString()}
          </p>
        </div>
      </div>
    </div>
  )

  const renderTabContent = () => {
    switch (activeTab) {
      case 'store':
        return renderStoreSettings()
      case 'notifications':
        return renderNotificationSettings()
      case 'backup':
        return renderBackupSettings()
      default:
        return (
          <div className="text-center py-12">
            <p className="text-gray-500 dark:text-gray-400">
              {activeTab.charAt(0).toUpperCase() + activeTab.slice(1)} settings coming soon...
            </p>
          </div>
        )
    }
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-2xl font-bold text-gray-900 dark:text-white">Settings</h2>
          <p className="text-gray-600 dark:text-gray-400 mt-1">
            Configure your store preferences and system settings
          </p>
        </div>
        <button
          onClick={handleSave}
          className="btn-primary flex items-center"
        >
          <Save className="h-4 w-4 mr-2" />
          Save Changes
        </button>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
        {/* Settings Navigation */}
        <div className="lg:col-span-1">
          <div className="card p-4">
            <nav className="space-y-1">
              {tabs.map((tab) => {
                const Icon = tab.icon
                return (
                  <button
                    key={tab.id}
                    onClick={() => setActiveTab(tab.id)}
                    className={`w-full flex items-center px-3 py-2 text-sm font-medium rounded-md transition-colors ${
                      activeTab === tab.id
                        ? 'bg-green-100 text-green-700 dark:bg-green-900/30 dark:text-green-400'
                        : 'text-gray-600 dark:text-gray-400 hover:bg-gray-50 dark:hover:bg-slate-700'
                    }`}
                  >
                    <Icon className="h-4 w-4 mr-3" />
                    {tab.label}
                  </button>
                )
              })}
            </nav>
          </div>
        </div>

        {/* Settings Content */}
        <div className="lg:col-span-3">
          <div className="card p-6">
            {renderTabContent()}
          </div>
        </div>
      </div>
    </div>
  )
}
