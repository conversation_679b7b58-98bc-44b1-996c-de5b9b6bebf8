{"scripts": {"dev": "cd sari-sari-admin && npm run dev", "build": "cd sari-sari-admin && npm run build", "start": "cd sari-sari-admin && npm run start", "lint": "cd sari-sari-admin && npm run lint"}, "dependencies": {"@headlessui/react": "^2.2.4", "@hookform/resolvers": "^5.1.1", "@supabase/supabase-js": "^2.50.3", "cloudinary": "^2.7.0", "date-fns": "^4.1.0", "lucide-react": "^0.525.0", "next-cloudinary": "^6.16.0", "react-hook-form": "^7.60.0", "zod": "^3.25.75"}}