'use client'

import { BarChart3, History, Calendar, Settings } from 'lucide-react'

interface SidebarProps {
  activeSection: string
  setActiveSection: (section: string) => void
}

export default function Sidebar({ activeSection, setActiveSection }: SidebarProps) {
  const menuItems = [
    {
      id: 'api-graphing',
      label: 'API Graphing & Visuals',
      icon: BarChart3,
      description: 'ECharts analytics and data visualization'
    },
    {
      id: 'history',
      label: 'History',
      icon: History,
      description: 'Transaction and activity logs'
    },
    {
      id: 'calendar',
      label: 'Calendar',
      icon: Calendar,
      description: 'Events and scheduling'
    },
    {
      id: 'settings',
      label: 'Settings',
      icon: Settings,
      description: 'Store configuration and preferences'
    },
  ]

  return (
    <div className="w-80 bg-white dark:bg-slate-800 shadow-lg border-r border-gray-200 dark:border-slate-700">
      <div className="p-6">
        <h2 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">
          Additional Tools
        </h2>
        <p className="text-sm text-gray-600 dark:text-gray-400">
          Advanced features and utilities
        </p>
      </div>

      <nav className="px-4">
        {menuItems.map((item) => {
          const Icon = item.icon
          const isActive = activeSection === item.id

          return (
            <button
              key={item.id}
              onClick={() => setActiveSection(item.id)}
              className={`w-full flex items-start p-4 mb-2 text-left rounded-xl transition-all duration-200 group ${
                isActive
                  ? 'bg-green-50 dark:bg-green-900/20 text-green-700 dark:text-green-400 border border-green-200 dark:border-green-800'
                  : 'text-gray-600 dark:text-gray-400 hover:bg-gray-50 dark:hover:bg-slate-700 border border-transparent'
              }`}
            >
              <div className={`p-2 rounded-lg mr-3 ${
                isActive
                  ? 'bg-green-100 dark:bg-green-900/40'
                  : 'bg-gray-100 dark:bg-slate-600 group-hover:bg-gray-200 dark:group-hover:bg-slate-500'
              }`}>
                <Icon className={`h-5 w-5 ${
                  isActive
                    ? 'text-green-600 dark:text-green-400'
                    : 'text-gray-500 dark:text-gray-400'
                }`} />
              </div>
              <div className="flex-1">
                <h3 className={`font-medium text-sm ${
                  isActive
                    ? 'text-green-700 dark:text-green-400'
                    : 'text-gray-900 dark:text-white'
                }`}>
                  {item.label}
                </h3>
                <p className={`text-xs mt-1 ${
                  isActive
                    ? 'text-green-600 dark:text-green-500'
                    : 'text-gray-500 dark:text-gray-400'
                }`}>
                  {item.description}
                </p>
              </div>
            </button>
          )
        })}
      </nav>

      <div className="absolute bottom-0 w-80 p-6 border-t border-gray-200 dark:border-slate-700">
        <div className="text-sm text-gray-500 dark:text-gray-400">
          <div className="flex items-center space-x-2 mb-2">
            <div className="w-6 h-6 hero-gradient rounded-md flex items-center justify-center">
              <span className="text-white font-bold text-xs">R</span>
            </div>
            <span className="font-medium">Revantad Store</span>
          </div>
          <p className="text-xs">Admin Dashboard v2.0</p>
          <p className="text-xs">Professional Business Management</p>
        </div>
      </div>
    </div>
  )
}
